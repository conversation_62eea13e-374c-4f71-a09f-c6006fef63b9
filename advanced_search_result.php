<?php
/*
  $Id$

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2010 osCommerce

  Released under the GNU General Public License
*/

// Start timing for performance debugging
$timing_start = microtime(true);
$timing_points = array();

require('includes/application_top.php');

$timing_points['application_top'] = microtime(true) - $timing_start;

require('includes/languages/' . $language . '/advanced_search.php');

$timing_points['language_file'] = microtime(true) - $timing_start;

$error = false;

if ((isset($_GET['keywords']) && empty($_GET['keywords'])) &&
    (isset($_GET['dfrom']) && (empty($_GET['dfrom']) || ($_GET['dfrom'] == DOB_FORMAT_STRING))) &&
    (isset($_GET['dto']) && (empty($_GET['dto']) || ($_GET['dto'] == DOB_FORMAT_STRING))) &&
    (isset($_GET['pfrom']) && !is_numeric($_GET['pfrom'])) &&
    (isset($_GET['pto']) && !is_numeric($_GET['pto']))) {
    $error = true;

    $messageStack->add_session('search', ERROR_AT_LEAST_ONE_INPUT);
} else {
    $dfrom = '';
    $dto = '';
    $pfrom = '';
    $pto = '';
    $keywords = '';

    if (isset($_GET['dfrom'])) {
        $dfrom = (($_GET['dfrom'] == DOB_FORMAT_STRING) ? '' : $_GET['dfrom']);
    }

    if (isset($_GET['dto'])) {
        $dto = (($_GET['dto'] == DOB_FORMAT_STRING) ? '' : $_GET['dto']);
    }

    if (isset($_GET['pfrom'])) {
        $pfrom = $_GET['pfrom'];
    }

    if (isset($_GET['pto'])) {
        $pto = $_GET['pto'];
    }
    if (isset($_GET['keywords'])) {
        $keywords = preg_replace('/[^A-Za-z0-9+-_]/', ' ', $_GET['keywords']);
        $keywords = tep_db_prepare_input($keywords);
    }

    $date_check_error = false;
    if (@tep_not_null($dfrom)) {
        if (!tep_checkdate($dfrom, DOB_FORMAT_STRING, $dfrom_array)) {
            $error = true;
            $date_check_error = true;

            $messageStack->add_session('search', ERROR_INVALID_FROM_DATE);
        }
    }

    if (@tep_not_null($dto)) {
        if (!tep_checkdate($dto, DOB_FORMAT_STRING, $dto_array)) {
            $error = true;
            $date_check_error = true;

            $messageStack->add_session('search', ERROR_INVALID_TO_DATE);
        }
    }

    if (($date_check_error == false) && @tep_not_null($dfrom) && @tep_not_null($dto)) {
        if (mktime(0, 0, 0, $dfrom_array[1], $dfrom_array[2], $dfrom_array[0]) > mktime(0, 0, 0, $dto_array[1], $dto_array[2], $dto_array[0])) {
            $error = true;

            $messageStack->add_session('search', ERROR_TO_DATE_LESS_THAN_FROM_DATE);
        }
    }

    $price_check_error = false;
    if (@tep_not_null($pfrom)) {
        if (!settype($pfrom, 'double')) {
            $error = true;
            $price_check_error = true;

            $messageStack->add_session('search', ERROR_PRICE_FROM_MUST_BE_NUM);
        }
    }

    if (@tep_not_null($pto)) {
        if (!settype($pto, 'double')) {
            $error = true;
            $price_check_error = true;

            $messageStack->add_session('search', ERROR_PRICE_TO_MUST_BE_NUM);
        }
    }

    if (($price_check_error == false) && is_float($pfrom) && is_float($pto)) {
        if ($pfrom >= $pto) {
            $error = true;

            $messageStack->add_session('search', ERROR_PRICE_TO_LESS_THAN_PRICE_FROM);
        }
    }

    if (@tep_not_null($keywords)) {
        if (!tep_parse_search_string($keywords, $search_keywords)) {
            $error = true;

            $messageStack->add_session('search', ERROR_INVALID_KEYWORDS);
        }
    }
}

if (empty($dfrom) && empty($dto) && empty($pfrom) && empty($pto) && empty($keywords)) {
    $error = true;

    $messageStack->add_session('search', ERROR_AT_LEAST_ONE_INPUT);
}

$timing_points['validation_complete'] = microtime(true) - $timing_start;

if ($error == true) {
    tep_redirect(tep_href_link('advanced_search.php&sort=6a&page=1', tep_get_all_get_params(), 'NONSSL', true, false));
}

$breadcrumb->add(NAVBAR_TITLE_1, tep_href_link('advanced_search.php'));
$breadcrumb->add(NAVBAR_TITLE_2, tep_href_link('advanced_search_result.php', tep_get_all_get_params(), 'NONSSL', true, false));

$timing_points['breadcrumb_setup'] = microtime(true) - $timing_start;

require('includes/template_top.php');

$timing_points['template_top'] = microtime(true) - $timing_start;
?>

    <div class="page-header">
        <h1 class="h3"><?php echo HEADING_TITLE_2; ?></h1>
    </div>

    <div class="contentContainer">

        <?php
        // create column list
        $define_list = array('PRODUCT_LIST_MODEL' => PRODUCT_LIST_MODEL,
            'PRODUCT_LIST_NAME' => PRODUCT_LIST_NAME,
            'PRODUCT_LIST_MANUFACTURER' => PRODUCT_LIST_MANUFACTURER,
            'PRODUCT_LIST_PRICE' => PRODUCT_LIST_PRICE,
            'PRODUCT_LIST_QUANTITY' => PRODUCT_LIST_QUANTITY,
            'PRODUCT_LIST_WEIGHT' => PRODUCT_LIST_WEIGHT,
            'PRODUCT_LIST_IMAGE' => PRODUCT_LIST_IMAGE,
            'PRODUCT_LIST_BUY_NOW' => PRODUCT_LIST_BUY_NOW,
            'PRODUCT_LIST_ORDER' => '99');

        asort($define_list);

        $timing_points['column_setup_start'] = microtime(true) - $timing_start;

        $column_list = array();
        foreach ($define_list as $key => $value) {
            if ($value > 0) $column_list[] = $key;
        }

        $timing_points['column_list_created'] = microtime(true) - $timing_start;

        $select_column_list = '';

        for ($i = 0, $n = sizeof($column_list); $i < $n; $i++) {
            switch ($column_list[$i]) {
                case 'PRODUCT_LIST_MODEL':
                    $select_column_list .= "IF(pv.model IS NOT NULL AND pv.model != '', pv.model, p.products_model) as products_model, ";
                    break;
                case 'PRODUCT_LIST_MANUFACTURER':
                    $select_column_list .= 'm.manufacturers_name, ';
                    break;
                case 'PRODUCT_LIST_QUANTITY':
                    $select_column_list .= 'p.products_quantity, ';
                    break;
                case 'PRODUCT_LIST_IMAGE':
                    $select_column_list .= 'p.products_image, ';
                    break;
                case 'PRODUCT_LIST_WEIGHT':
                    $select_column_list .= 'p.products_weight, ';
                    break;
            }
        }

        $select_str = "select distinct
          " . $select_column_list . "
          m.manufacturers_id,
          IF(pv.attributes IS NOT NULL AND pv.attributes != '', concat(p.products_id,pv.attributes), p.products_id) as products_id,
          pv.products_variations_id,
          pv.attributes,
          SUBSTRING_INDEX(pd.products_description, ' ', 20) as products_description,
          pd.products_name,
          COALESCE(NULLIF(pvp.variations_price, 0), p.products_price) AS products_price,
          p.products_tax_class_id, IF(s.status IS NOT NULL AND s.status != '', s.specials_new_products_price, NULL) as specials_new_products_price,
             IF(s.status IS NOT NULL AND s.status != '', s.specials_new_products_price, products_price) as final_price, IF(s.status IS NOT NULL AND s.status != '', 1, 0) as is_special ";

        if ((DISPLAY_PRICE_WITH_TAX == 'true') && (@tep_not_null($pfrom) || @tep_not_null($pto))) {
            $select_str .= ", SUM(tr.tax_rate) as tax_rate ";
        }

        $from_str = "FROM " . TABLE_PRODUCTS . " p
                LEFT JOIN " . TABLE_MANUFACTURERS . " m USING(manufacturers_id)
                LEFT JOIN " . TABLE_SPECIALS . " s ON p.products_id = s.products_id
                LEFT JOIN products_attributes pa ON p.products_id = pa.products_id AND pa.attribute_default = 1
                LEFT JOIN products_variations pv ON p.products_id = pv.products_id
                LEFT JOIN (
                    SELECT products_id, MIN(price) as variations_price
                    FROM products_variations
                    GROUP BY products_id
                ) pvp ON p.products_id = pvp.products_id";

        if ((DISPLAY_PRICE_WITH_TAX == 'true') && (@tep_not_null($pfrom) || @tep_not_null($pto))) {
            if (!tep_session_is_registered('customer_country_id')) {
                $customer_country_id = STORE_COUNTRY;
                $customer_zone_id = STORE_ZONE;
            }
            $from_str .= " left join " . TABLE_TAX_RATES . " tr on p.products_tax_class_id = tr.tax_class_id left join " . TABLE_ZONES_TO_GEO_ZONES . " gz on tr.tax_zone_id = gz.geo_zone_id and (gz.zone_country_id is null or gz.zone_country_id = '0' or gz.zone_country_id = '" . (int)$customer_country_id . "') and (gz.zone_id is null or gz.zone_id = '0' or gz.zone_id = '" . (int)$customer_zone_id . "')";
        }

        $from_str .= ", " . TABLE_PRODUCTS_DESCRIPTION . " pd, " . TABLE_CATEGORIES . " c, " . TABLE_PRODUCTS_TO_CATEGORIES . " p2c";

        $timing_points['from_clause_built'] = microtime(true) - $timing_start;

        $where_str = " WHERE p.products_status = '1' AND p.products_id = pd.products_id AND pd.language_id = '" . (int)$languages_id . "' AND p.products_id = p2c.products_id AND p2c.categories_id = c.categories_id ";

        if (isset($_GET['categories_id']) && @tep_not_null($_GET['categories_id'])) {
            if (isset($_GET['inc_subcat']) && ($_GET['inc_subcat'] == '1')) {
                $subcategories_array = array();
                tep_get_subcategories($subcategories_array, $_GET['categories_id']);

                $where_str .= " and p2c.products_id = p.products_id and p2c.products_id = pd.products_id and (p2c.categories_id = '" . (int)$_GET['categories_id'] . "'";

                for ($i = 0, $n = sizeof($subcategories_array); $i < $n; $i++) {
                    $where_str .= " or p2c.categories_id = '" . (int)$subcategories_array[$i] . "'";
                }

                $where_str .= ")";
            } else {
                $where_str .= " and p2c.products_id = p.products_id and p2c.products_id = pd.products_id and pd.language_id = '" . (int)$languages_id . "' and p2c.categories_id = '" . (int)$_GET['categories_id'] . "'";
            }
        }

        if (isset($_GET['manufacturers_id']) && @tep_not_null($_GET['manufacturers_id'])) {
            $where_str .= " and m.manufacturers_id = '" . (int)$_GET['manufacturers_id'] . "'";
        }

        if (isset($search_keywords) && (sizeof($search_keywords) > 0)) {
            $where_str .= " AND (";
            for ($i = 0, $n = sizeof($search_keywords); $i < $n; $i++) {
                switch ($search_keywords[$i]) {
                    case '(':
                    case ')':
                    case 'and':
                    case 'or':
                        $where_str .= " " . $search_keywords[$i] . " ";
                        break;
                    default:
                        $keyword = tep_db_prepare_input($search_keywords[$i]);
                        $where_str .= "(";
                        // Use separate FULLTEXT searches for better performance
                        $where_str .= "MATCH(pd.products_seo_keywords, pd.products_name) AGAINST('" . tep_db_input($keyword) . "' IN BOOLEAN MODE) OR ";
                        $where_str .= "MATCH(p.products_model) AGAINST('" . tep_db_input($keyword) . "' IN BOOLEAN MODE) OR ";
                        $where_str .= "(pv.model IS NOT NULL AND MATCH(pv.model) AGAINST('" . tep_db_input($keyword) . "' IN BOOLEAN MODE)) OR ";
                        $where_str .= "(m.manufacturers_name IS NOT NULL AND MATCH(m.manufacturers_name) AGAINST('" . tep_db_input($keyword) . "' IN BOOLEAN MODE))";
                        if (isset($_GET['search_in_description']) && ($_GET['search_in_description'] == '1'))
                            $where_str .= " OR pd.products_description LIKE '%" . tep_db_input($keyword) . "%'";
                        $where_str .= ")";
                        break;
                }
            }
            $where_str .= " )";
        }

        if (@tep_not_null($dfrom)) {
            $where_str .= " and p.products_date_added >= '" . tep_date_raw($dfrom) . "'";
        }

        if (@tep_not_null($dto)) {
            $where_str .= " and p.products_date_added <= '" . tep_date_raw($dto) . "'";
        }

        if (@tep_not_null($pfrom)) {
            if ($currencies->is_set($currency)) {
                $rate = $currencies->get_value($currency);

                $pfrom = $pfrom / $rate;
            }
        }

        if (@tep_not_null($pto)) {
            if (isset($rate)) {
                $pto = $pto / $rate;
            }
        }

        if (DISPLAY_PRICE_WITH_TAX == 'true') {
            if ($pfrom > 0) $where_str .= " and (IF(s.status IS NOT NULL AND s.status != '', s.specials_new_products_price, p.products_price) * if(gz.geo_zone_id is null, 1, 1 + (tr.tax_rate / 100) ) >= " . (double)$pfrom . ")";
            if ($pto > 0) $where_str .= " and (IF(s.status IS NOT NULL AND s.status != '', s.specials_new_products_price, p.products_price) * if(gz.geo_zone_id is null, 1, 1 + (tr.tax_rate / 100) ) <= " . (double)$pto . ")";
        } else {
            if ($pfrom > 0) $where_str .= " and (IF(s.status IS NOT NULL AND s.status != '', s.specials_new_products_price, p.products_price) >= " . (double)$pfrom . ")";
            if ($pto > 0) $where_str .= " and (IF(s.status IS NOT NULL AND s.status != '', s.specials_new_products_price, p.products_price) <= " . (double)$pto . ")";
        }
        $where_str .= " and p2c.categories_id != '*********' and  p2c.categories_id != '499'";

        $timing_points['where_clause_built'] = microtime(true) - $timing_start;

        if ((DISPLAY_PRICE_WITH_TAX == 'true') && (@tep_not_null($pfrom) || @tep_not_null($pto))) {
            $where_str .= " group by p.products_id, tr.tax_priority";
        }

        if ((!isset($_GET['sort'])) || (!preg_match('/^[1-8][ad]$/', $_GET['sort'])) || (substr($_GET['sort'], 0, 1) > sizeof($column_list))) {
            for ($i = 0, $n = sizeof($column_list); $i < $n; $i++) {
                /*
                if ($column_list[$i] == 'PRODUCT_LIST_NAME') {
               $_GET['sort'] = $i+1 . 'a';
               $order_str = " order by pd.products_name";
               break;
             }
           }
               */
                if ($column_list[$i] == 'PRODUCT_LIST_ORDER') {
                    $_GET['sort'] = $i . 'a';
                    $order_str = " order by p.products_sort_order";
                    break;
                }
            }
        } else {

            $sort_col = substr($_GET['sort'], 0, 1);
            $sort_order = substr($_GET['sort'], 1);

            switch ($column_list[$sort_col - 1]) {
                case 'PRODUCT_LIST_MODEL':
                    $order_str = " order by p.products_model " . ($sort_order == 'd' ? 'desc' : '') . ", pd.products_name";
                    break;
                case 'PRODUCT_LIST_NAME':
                    $order_str = " order by pd.products_name " . ($sort_order == 'd' ? 'desc' : '');
                    break;
                case 'PRODUCT_LIST_MANUFACTURER':
                    $order_str = " order by m.manufacturers_name " . ($sort_order == 'd' ? 'desc' : '') . ", pd.products_name";
                    break;
                case 'PRODUCT_LIST_QUANTITY':
                    $order_str = " order by p.products_quantity " . ($sort_order == 'd' ? 'desc' : '') . ", pd.products_name";
                    break;
                case 'PRODUCT_LIST_IMAGE':
                    $order_str = " order by pd.products_name";
                    break;
                case 'PRODUCT_LIST_WEIGHT':
                    $order_str = " order by p.products_weight " . ($sort_order == 'd' ? 'desc' : '') . ", pd.products_name";
                    break;
                case 'PRODUCT_LIST_PRICE':
                    $order_str = " order by final_price " . ($sort_order == 'd' ? 'desc' : '') . ", pd.products_name";
                    break;
                case 'PRODUCT_LIST_ORDER':
                    $order_str = " order by p.products_sort_order " . ($sort_order == 'd' ? 'desc' : '') . ", pd.products_name";
                    break;
            }
        }

        $timing_points['sql_construction'] = microtime(true) - $timing_start;

        $listing_sql = $select_str . $from_str . $where_str . $order_str;


        $timing_points['before_product_listing'] = microtime(true) - $timing_start;

        require('includes/modules/product_listing.php');

        $timing_points['product_listing_complete'] = microtime(true) - $timing_start;
        ?>

        <br/>

        <div class="buttonSet">
            <?php echo tep_draw_button(IMAGE_BUTTON_BACK, 'fa fa-angle-left', tep_href_link('advanced_search.php', tep_get_all_get_params(array('sort', 'page')), 'NONSSL', true, false)); ?>
        </div>

        <?php
        // Display timing information for debugging
        $timing_points['page_complete'] = microtime(true) - $timing_start;

        if (isset($_GET['debug_timing']) && $_GET['debug_timing'] == '1') {
            echo '<div class="alert alert-info" style="margin-top: 20px;">';
            echo '<h4>Performance Debug Information</h4>';
            echo '<table class="table table-sm">';
            echo '<thead><tr><th>Checkpoint</th><th>Time (seconds)</th><th>Cumulative (seconds)</th></tr></thead>';
            echo '<tbody>';

            $previous_time = 0;
            foreach ($timing_points as $checkpoint => $time) {
                $duration = $time - $previous_time;
                echo '<tr>';
                echo '<td>' . htmlspecialchars($checkpoint) . '</td>';
                echo '<td>' . number_format($duration, 4) . '</td>';
                echo '<td>' . number_format($time, 4) . '</td>';
                echo '</tr>';
                $previous_time = $time;
            }

            echo '</tbody>';
            echo '</table>';
            echo '<p><strong>Total execution time: ' . number_format($timing_points['page_complete'], 4) . ' seconds</strong></p>';
            echo '<p><em>To see this timing information, add &debug_timing=1 to the URL</em></p>';
            echo '</div>';
            echo '<pre>' . print_r($listing_sql, true) . '</pre>';
        }
        ?>
    </div>

<?php
require('includes/template_bottom.php');
require('includes/application_bottom.php');
?>