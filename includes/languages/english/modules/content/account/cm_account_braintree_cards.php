<?php
/*
  $Id$

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2014 osCommerce

  Released under the GNU General Public License
*/

  define('MODULE_CONTENT_ACCOUNT_BRAINTREE_CARDS_TITLE', 'Braintree Cards Management Page');
  define('MODULE_CONTENT_ACCOUNT_BRAINTREE_CARDS_DESCRIPTION', 'Adds a Braintree Cards management page to the My Account area');

  define('MODULE_CONTENT_ACCOUNT_BRAINTREE_CARDS_ERROR_MAIN_MODULE', 'This module will not load until the Braintree payment module has been installed, configured, and is enabled. Please install and configure the Braintree payment module.');

  define('MODULE_CONTENT_ACCOUNT_BRAINTREE_CARDS_LINK_TITLE', 'Manage saved payment cards.');

  define('MODULE_CONTENT_ACCOUNT_BRAINTREE_CARDS_NAVBAR_TITLE_1', 'My Account');
  define('MODULE_CONTENT_ACCOUNT_BRAINTREE_CARDS_NAVBAR_TITLE_2', 'Saved Cards');

  define('MODULE_CONTENT_ACCOUNT_BRAINTREE_CARDS_HEADING_TITLE', 'Saved Cards');
  define('MODULE_CONTENT_ACCOUNT_BRAINTREE_CARDS_TEXT_DESCRIPTION', '<p>Saved payment cards are stored securely and safely in a certified and audited PCI DSS compliant environment. This high level of security provides convenience in allowing saved cards to be used for next purchases without the card information having to be re-typed again for each order made.</p><p>New cards can be securely saved during the process of your next order.</p>');

  define('MODULE_CONTENT_ACCOUNT_BRAINTREE_CARDS_SAVED_CARDS_TITLE', 'Saved Cards');
  define('MODULE_CONTENT_ACCOUNT_BRAINTREE_CARDS_TEXT_NO_CARDS', '<p>No cards have been saved yet.</p>');

  define('MODULE_CONTENT_ACCOUNT_BRAINTREE_CARDS_SUCCESS_DELETED', 'The card has been successfully deleted.');
?>
