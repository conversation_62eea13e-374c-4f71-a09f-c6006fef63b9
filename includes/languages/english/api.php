<?php
require('includes/application_top.php');
header('Access-Control-Allow-Origin: ' . HTTPS_SERVER);
header('Access-Control-Allow-Headers: Origin, X-Requested-With, Content-Type, Accept');


	if (isset($_GET['getProd'])) {
		$response = getProd($_GET['id']);
		echo json_encode($response);
	} elseif (isset($_GET['sendEmail'])) {
		$response = sendEmail($_GET);
		echo json_encode($response);
	} elseif (isset($_GET['adminViewFamilies'])) {
		$response = adminViewFamilies($_GET['id'], $_GET['family_id']);
		echo json_encode($response);
	} elseif (isset($_GET['getNewData'])) {
	//	echo "test";
		$response = getNewData($_GET['getNewData'], $_GET['attribs']);
		echo json_encode($response);		
	} elseif (isset($_GET['addToCart'])) {
		require_once('includes/classes/attributes.class.php');
		$response = addToCart($_GET['products_id'],$_GET['attribs']);
		echo json_encode($response);		
	} elseif (isset($_GET['update_cart_quantity'])) {
		require_once('includes/classes/attributes.class.php');
		$response = update_cart_quantity($_GET['products']); 
		echo json_encode($response);	
	}

function getProd($id) {
    $response = '';
    $input = tep_db_prepare_input($id);
    $input = tep_db_input($input);

    if (is_numeric($input)) {
        $sqlQuery = "SELECT * FROM products p, products_description pd WHERE p.products_id = '" . $input . "' AND p.products_id = pd.products_id";
        $sql = tep_db_query($sqlQuery);
        $response = array();

        if ($rows = tep_db_fetch_array($sql)) {
            extract($rows);
            $response = array(
                "product" => array(
                    "name" => $products_name,
                    "id" => $products_id,
                    "model" => $products_model,
                    "price" => $products_price
                )
            );
        }
    }
    return $response;
}
 
function sendEmail($postData) {
    $response = '';

    $name = $postData['name'];
    $company = $postData['company'];
    $address = $postData['address'];
    $telephone = $postData['telephone'];
    $fax = $postData['fax'];
    $email = $postData['email'];
    $capture = $postData['capture'];
    $message = $postData['message'];
    $captureCorrect = 0;

    if (strtolower(str_replace(' ', '', $capture)) == 'aekt') {
        $form_details =  "Name: " . $name . "\nCompany: " . $company . "\nAddress: " . $address . "\nPhone: " . $telephone . "\nFax: " . $fax . "\nE-Mail: " .  $email . "\nMessage: \n" . $message;
        $recipient = '<EMAIL>';
        $subject = 'Enquiry - CADandBIM.co.uk Site';
        $from = $email;
        mail($recipient, $subject, $form_details, "FROM: $from \n");
    }

    return $response;
}

function adminViewFamilies($id, $familyId) {
    $response = '';

    if (is_numeric($id)) {
        $sql = tep_db_query("SELECT distinct products_id FROM products_families WHERE family_id = '" . $familyId . "'");
        $response = array();

        if ($rows = tep_db_fetch_array($sql)) {
            extract($rows);
            $response = array(
                "familyItems" => array(
                    "id" => $products_id
                )
            );
        }
    }

    return $response;
}

function getNewData($productId, $attribString) {
	global $currencies, $languages_id,$products_attributes;
    include('includes/languages/english/modules/content/product_info/cm_pi_options_attributes.php');
    include('includes/modules/content/product_info/cm_pi_options_attributes.php');
    $response = new cm_pi_options_attributes();
    echo $response->execute();
	/*
	 *
		require_once('includes/classes/attributes.class.php');
		$products_attributes = new tcs_product_attributes($productId,1,$attribString);
		$products_attributes->get_attributes(false);
		$products_attributes->get_variations();
		////print_rr($products_attributes);
		$response = array();
		$productId = explode('{', $productId)[0];
		$products_query = "
		select * 
		from 
			products p,
			products_description pd 
		where 
			p.products_id = '" . $productId . "' 
		and 
			p.products_id = pd.products_id";
		
		$products_sql = tep_db_query($products_query);
		$product = tep_db_fetch_array($products_sql);
		
		$products_id = $product['products_id'];
		$products_name = $product['products_name'];
		$products_gtin = $product['products_gtin'];
		$products_model = $product['products_model'];
		$products_url =  tep_href_link('product_info.php', 'products_id=' . $product['products_id'], 'SSL', false);
		$products_price = $product['products_price'];
		$products_tax_class_id = $product['products_tax_class_id'];
		$attribs = $products_attributes->get_current_selected_attributes();
	
		if (count($attribs) > 0){
			foreach ($attribs as $aKey => $attrib) {
				foreach ($attrib as $vKey => $values) {
					if ($values['price_prefix'] == '+') {
						$options_values_price = $values['options_values_price'];
						$products_price += $options_values_price;
					} else if ($values['price_prefix'] == '*') {
						$options_values_price = $values['options_values_price'] > 0 ?  $product['products_price'] * $values['options_values_price'] : $product['products_price'];
						$products_price = $options_values_price;
					} else if ($values['price_prefix'] == '-') {
						$options_values_price = 0 - $values['options_values_price'];
						$products_price -= $options_values_price;
					}
				}
			}
		}
		
		//echo $products_price;
		$variations = $products_attributes->variations;
		//while ($variations = tep_db_fetch_array($products_variations_sql)) {
		// print_rr($variations);
		
		foreach ($variations as $variation){
			if ($products_attributes->compare_attributes($variation['attribute_string'],$attribString)){
				$product_name_attribed = $products_name . $products_attributes->generate_product_suffix($variation['attribute_string']);
				$products_name  = $product_name_attribed;
				$products_model = $variation['model'];
				$image_id = $variation['image_id'];
				$products_gtin  = $variation['gtin'];
				$products_price = $variation['price'];
				$products_url = tep_href_link('product_info.php', 'products_id=' . $productId . $variation['attribute_string'], 'SSL', false);
				$attributes_row = $variation;
				break;
			}
		}
			if (isset($image_id)){
				$pi_query = tep_db_query("select id, image, htmlcontent from products_images where id = " . $variation['image_id'] . " order by sort_order");
				$pi_total = tep_db_num_rows($pi_query);
				$pi_counter = 0;
				while ($pi = tep_db_fetch_array($pi_query)) {
					$image_string = tep_image('images/' . $pi['image'], htmlspecialchars ($products_name) . ' ' . $pi_counter, KISSIT_MAIN_PRODUCT_IMAGE_WIDTH, KISSIT_MAIN_PRODUCT_IMAGE_HEIGHT, 'id="piGalImg_' . $pi['id'] . '" '. ((KISSIT_MAIN_PRODUCT_WATERMARK_SIZE > 0)? preg_replace('%<img width="[0-9 ]+" height="[0-9 ]+" src="(.*)title=.+%', 'data-highres="$1', tep_image('images/' . $pi['image'], null, $width, $height)) : 'data-highres="'. 'images/' . $pi['image'] . '"'));
					$pi_counter++;
				}
			}else{
				$image_string = "";
			}
			$products_price_output = tcs_display_price($product['products_id'],$variation['price'], $products_tax_class_id,true,$attribString);
			//$VAT = '<br><span id="productsPriceIncTax">(ex VAT ' . $currencies->display_price($products_price+tep_calculate_tax($products_price,tep_get_tax_rate($products_tax_class_id)), tep_get_tax_rate($products_tax_class_id)) . ' Inc. VAT' . ')</span>';
			
			
			$price_string = '
			<div>
				<div class="productsPrice text-right-not-xs">
				<div class="text-right-not-xs productsPrice">' . $products_price_output . '</div>
				</div>
			</div>';
		
		$response[] = array(
			"title" => $products_name,
			"attribute_id" => "",
			"product_id" => $product['products_id'],
			"products_name" => $products_name,	
			"image_id" => $image_id,
			"image_string" => htmlEntities($image_string),
			"products_gtin" => $products_gtin,
			"products_model" => $products_model,
			"products_url" => htmlEntities($products_url),
			"products_price" => htmlEntities($price_string)
		);
		*/
		//print_rr($response);
		return '';
	}

//[{"Success":1,"qty":1,"products_id":11165,"Name":"HP No.712 Ink Cartridge, Black, 38ml, Single Pack","new_cart_total_items":3,"new_cart_total_price":596}]
function addToCart($productsId, $attribute_string = "") {
	global $cart;
		
		$products_id = (int)$_GET['products_id'];        		
        $products_attributes = new tcs_product_attributes($productsId,1,$attribute_string);

		$attributes = $products_attributes->parse_attribute_string( $attribute_string);
		////print_rr($attributes);
		$cart->add_cart($productsId, $cart->get_quantity(tep_get_uprid($products_id, $attributes))+1, $attributes);		
		
		////print_rr($products_attributes->generate_product_suffix($attribute_string));
		$response[] = array(
			"Success" => 1,
			"products_id" => $products_id,
			"qty" =>  1,
			"attribute_string" => $attribute_string,
			"products_id" => $productsId . $attribute_string,
			"new_cart_total_price"=> $cart->show_total(),	
			"new_cart_total_items"=> $cart->count_contents(),			
			"Name" => tep_get_products_name($productsId) . $products_attributes->generate_product_suffix($attribute_string),
			"url" =>  tep_href_link('product_info.php', 'products_id=' . $productsId . $attribute_string, 'SSL', false)
		);
	return $response;

	//$messageStack->add_session('product_action', sprintf(PRODUCT_ADDED, , 'success');
    
	//tep_redirect(tep_href_link($goto, tep_get_all_get_params($parameters)));      
}
function update_cart_quantity($products_json) {
	global $cart, $currencies;
	$attributes = "";
	$products = json_decode(stripslashes($products_json), true);
	for ($i=0; $i < sizeof($products); $i++) {
		$products_id = $products[$i]['id'];
		$products_qty = $products[$i]['qty'];
		if (strpos($products_id, "{") > 0) {
			$attribute_string = '{' . explode('{',$products_id,2)[1];
			$products_attributes = new tcs_product_attributes($products_id,1,$attribute_string);
			$attributes = $products_attributes->parse_attribute_string( $attribute_string);
		}
		$cart->add_cart($products_id, $products_qty, $attributes, false);
	}
  
	$cart_products = $cart->get_products();
	for	($i=0; $i < sizeof($cart_products); $i++) {
		$products_id = $cart_products[$i]['id'];
		$products_qty = $cart_products[$i]['quantity'];
		$attributes = $cart_products[$i]['attributes'];
		$products_out[$i] = array(
			"id" => $products_id,
			"qty" => $products_qty,
			"attributes" => $attributes,
			"final_price" =>  $currencies->display_price($cart_products[$i]['final_price'], tep_get_tax_rate($cart_products[$i]['tax_class_id']), $cart_products[$i]['quantity'])
		);
	}

	$response[] = array(
		"Success" => 1,
		"products_qty" =>  $cart->get_quantity(tep_get_uprid($products_id, $attributes)),
		"products" => $products_out,
		"new_cart_total_price"=> $currencies->format($cart->show_total())	
	);
	return $response;

	//$messageStack->add_session('product_action', sprintf(PRODUCT_ADDED, , 'success');
    
	//tep_redirect(tep_href_link($goto, tep_get_all_get_params($parameters)));      
}
?>


